{"name": "ppwa", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "cap:build": "npm run build && npx cap sync", "cap:android": "npm run cap:build && npx cap open android", "cap:sync": "npx cap sync"}, "dependencies": {"@capacitor/android": "^7.4.2", "@capacitor/app": "^7.0.2", "@capacitor/device": "^7.0.2", "@capacitor/geolocation": "^7.1.4", "@tailwindcss/vite": "^4.1.11", "daisyui": "^5.0.50", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@capacitor/cli": "^7.4.2", "@capacitor/core": "^7.4.2", "@eslint/js": "^9.32.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.0", "vite": "^7.1.0"}}